﻿using login.classes;
using login.classes.JsonQueuing;
using login.wsThemisAdmin2010;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Script.Services;
using System.Web.Services;
using System.Xml.Linq;

namespace login.pages
{
    public partial class QueuingSettings : basePage
    {

        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public Queuing Queuing { get; set; } = new Queuing();
        public string QueuingJson { get; set; }

        public string StructureId { get; set; }
        public string LangCode { get; set; }

        public string DefaultQueuingV2GotoUrlIndiv { get; set; }
        public bool IsSuperAdmin { get; set; } = false;
        public bool ShowCreateQueueButton { get; set; } = false;

        protected void Page_Load(object sender, EventArgs e)
        {


            if (!Page.IsPostBack)
            {
                // Vérifier si l'utilisateur est super admin via la session (pour les sections normales)
                bool isSuperAdminFromSession = System.Web.HttpContext.Current.Session["superAdmin"] != null &&
                                             Convert.ToBoolean(System.Web.HttpContext.Current.Session["superAdmin"].ToString()) == true;

                // Pour les sections normales, utiliser la logique habituelle
                IsSuperAdmin = isSuperAdminFromSession;

                // Pour le bouton spécifique, vérifier aussi le paramètre context
                string contextParam = Request.QueryString["context"] ?? "";
                ShowCreateQueueButton = isSuperAdminFromSession && contextParam.Equals("superadmin", StringComparison.OrdinalIgnoreCase);

                log.Debug($"IsSuperAdmin: {IsSuperAdmin}, ShowCreateQueueButton: {ShowCreateQueueButton}, Context: {contextParam}");

                LoadDatasQueuing();
            }



        }


        private void LoadDatasQueuing()
        {
            try
            {

                int structureId = int.Parse(Session["idstructure"].ToString());
                log.Debug($"QueuingSettings - page load structure id : {structureId}");
                string langCode = MyConfigurationManager.GetUserLanguage();
                LangCode = langCode;

                string queuingStructuresJson = MyConfigurationManager.AppSettings("QueuingStructuresJson");

                StructureId = structureId.ToString("0000");

                DefaultQueuingV2GotoUrlIndiv = MyConfigurationManager.AppSettings("QueuingV2Url");

                Queuing = new Queuing
                {
                    QueuingV1 = new QueuingV1(),
                    QueuingV2 = new QueuingV2(),
                    Maintenance = new Maintenance()
                };

                string jsonString = File.ReadAllText(queuingStructuresJson);
                JsonRoot myDeserializedClass = JsonConvert.DeserializeObject<JsonRoot>(jsonString);
                ListConf jsonConf = myDeserializedClass.ListConf.Where(s => s.StructureId == structureId).FirstOrDefault();

                if (jsonConf != null)
                {
                    if (!string.IsNullOrEmpty(jsonConf.IsOpen))
                    {
                        Queuing.QueuingV2.IsOpen = jsonConf.IsOpen.ToLower() == "y";
                        Queuing.QueuingV2.IsActive = Queuing.QueuingV2.IsOpen;
                    }
                    else
                    {
                        Queuing.QueuingV2.IsOpen = false;
                        Queuing.QueuingV2.IsActive = false;
                    }

                    Queuing.QueuingV2.IsActiveFileAttente = jsonConf.IsActiveFileAttente;

                    Queuing.QueuingV2.IsCaptcha = jsonConf.Iscaptcha;
                    Queuing.QueuingV2.MaxActiveUsers = jsonConf.MaxWT;
                    Queuing.QueuingV2.UsersPerTick = jsonConf.Npartick;
                    Queuing.QueuingV2.UrlToGo = jsonConf.UrlToGo;
                    Queuing.QueuingV2.DbType = jsonConf.DbType;

                    Queuing.QueuingV2.Url = MyConfigurationManager.AppSettings("QueuingV2Url");
                    Queuing.QueuingV2.UrlIndiv = MyConfigurationManager.AppSettings("QueuingV2Url");
                    Queuing.QueuingV2.UrlAbo = MyConfigurationManager.AppSettings("QueuingV2Url");
                }

                //"ThemisIniPath"
                string physiqueXmlPath = MyConfigurationManager.AppSettings("ThemisIniPath");
                physiqueXmlPath = physiqueXmlPath.Replace("[idstructureSur4zeros]", structureId.ToString("0000"));

                log.Debug($"physiqueXmlPath : {physiqueXmlPath}");

                if (File.Exists(physiqueXmlPath))
                {
                    XDocument doc = new XDocument();
                    doc = XDocument.Load(physiqueXmlPath);

                    Queuing.QueuingV1.Url = MyConfigurationManager.AppSettings("QueuingV1Url");
                    Queuing.QueuingV2.Url = MyConfigurationManager.AppSettings("QueuingV2Url");
                    Queuing.Maintenance.Url = MyConfigurationManager.AppSettings("MaintenanceUrl");

                    IEnumerable<XElement> sectionsList = from section in doc.Elements("configIni").Elements("Section")
                                                         select section;


                    int nbSections = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FILEATTENTE", StringComparison.OrdinalIgnoreCase)).Count();
                    //int nbSectionssss = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FILEATTENTE", StringComparison.OrdinalIgnoreCase)).Count();
                    log.Debug($"nbSections : {nbSections}");

                    if (nbSections > 0)
                    {
                        //au moins une section FILEATTENTE

                        IEnumerable<XElement> sectionsFileAttente = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FILEATTENTE", StringComparison.OrdinalIgnoreCase));
                        IEnumerable<XElement> sectionsFileAttenteQueuingV1 = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FILEATTENTE_QUEUINGV1", StringComparison.OrdinalIgnoreCase));
                        IEnumerable<XElement> sectionsFileAttenteQueuingV2 = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FILEATTENTE_QUEUINGV2", StringComparison.OrdinalIgnoreCase));
                        IEnumerable<XElement> sectionsFileAttenteMaintenance = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FILEATTENTE_MAINTENANCE", StringComparison.OrdinalIgnoreCase));

                        if (sectionsFileAttenteQueuingV1.Count() > 0)
                        {
                            FillQueuing(sectionsFileAttenteQueuingV1, false);
                        }

                        // Traitement des sections spécifiques - mais seulement pour charger les données, pas pour activer
                        if (sectionsFileAttenteQueuingV1.Count() > 0)
                        {
                            FillQueuing(sectionsFileAttenteQueuingV1, false);
                        }

                        if (sectionsFileAttenteQueuingV2.Count() > 0)
                        {
                            Queuing.QueuingV2.ViaQueuingSite = sectionsFileAttenteQueuingV2.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("VIAQUEUINGSITE", StringComparison.OrdinalIgnoreCase))?.Value ?? "";
                            Queuing.QueuingV2.UrlIndiv = sectionsFileAttenteQueuingV2.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("QUEUINGPAGEINDIV", StringComparison.OrdinalIgnoreCase))?.Value ?? "";
                            Queuing.QueuingV2.UrlAbo = sectionsFileAttenteQueuingV2.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("QUEUINGPAGEABOV2", StringComparison.OrdinalIgnoreCase))?.Value ?? "";
                        }

                        if (sectionsFileAttenteMaintenance.Count() > 0)
                        {
                            FillQueuing(sectionsFileAttenteMaintenance, true);
                        }


                        // Déterminer quelle section est active basé sur les sections FILEATTENTE avec attribut type
                        // D'abord, réinitialiser tous les IsActive à false
                        Queuing.QueuingV1.IsActive = false;
                        Queuing.QueuingV2.IsActive = false;
                        Queuing.QueuingV2.IsActiveFileAttente = false;
                        Queuing.Maintenance.IsActive = false;

                        bool foundActiveSection = false;

                        // Vérifier les sections FILEATTENTE avec attribut type
                        if (sectionsFileAttente.Count() > 0)
                        {
                            foreach (var section in sectionsFileAttente)
                            {
                                var typeAttribute = section.Attributes("type").FirstOrDefault();
                                string typeValue = typeAttribute?.Value ?? "";

                                if (typeValue == "v1")
                                {
                                    FillQueuing(new[] { section }, false);
                                    Queuing.QueuingV1.IsActive = true;
                                    foundActiveSection = true;
                                    break;
                                }
                                else if (typeValue == "maintenance")
                                {
                                    FillQueuing(new[] { section }, true);
                                    Queuing.Maintenance.IsActive = true;
                                    foundActiveSection = true;
                                    break;
                                }
                                else if (typeValue == "v2")
                                {
                                    Queuing.QueuingV2.IsActive = true;
                                    Queuing.QueuingV2.IsActiveFileAttente = true;
                                    Queuing.QueuingV2.Url = section.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("QUEUINGPAGE", StringComparison.OrdinalIgnoreCase))?.Value ?? "";
                                    Queuing.QueuingV2.UrlIndiv = section.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("QUEUINGPAGEINDIV", StringComparison.OrdinalIgnoreCase))?.Value ?? "";
                                    Queuing.QueuingV2.UrlAbo = section.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("QUEUINGPAGEABOV2", StringComparison.OrdinalIgnoreCase))?.Value ?? "";
                                    foundActiveSection = true;
                                    break;
                                }
                            }
                        }

                        // Si aucune section FILEATTENTE avec type n'est trouvée, utiliser les sections FILEATTENTE_* avec attribut type
                        if (!foundActiveSection)
                        {
                            // Vérifier FILEATTENTE_QUEUINGV1 avec attribut type
                            foreach (var section in sectionsFileAttenteQueuingV1)
                            {
                                var typeAttribute = section.Attributes("type").FirstOrDefault();
                                string typeValue = typeAttribute?.Value ?? "";

                                if (typeValue == "v1")
                                {
                                    Queuing.QueuingV1.IsActive = true;
                                    foundActiveSection = true;
                                    break;
                                }
                            }

                            // Vérifier FILEATTENTE_QUEUINGV2 avec attribut type
                            if (!foundActiveSection)
                            {
                                foreach (var section in sectionsFileAttenteQueuingV2)
                                {
                                    var typeAttribute = section.Attributes("type").FirstOrDefault();
                                    string typeValue = typeAttribute?.Value ?? "";

                                    if (typeValue == "v2")
                                    {
                                        Queuing.QueuingV2.IsActive = true;
                                        Queuing.QueuingV2.IsActiveFileAttente = true;
                                        foundActiveSection = true;
                                        break;
                                    }
                                }
                            }

                            // Vérifier FILEATTENTE_MAINTENANCE avec attribut type
                            if (!foundActiveSection)
                            {
                                foreach (var section in sectionsFileAttenteMaintenance)
                                {
                                    var typeAttribute = section.Attributes("type").FirstOrDefault();
                                    string typeValue = typeAttribute?.Value ?? "";

                                    if (typeValue == "maintenance")
                                    {
                                        Queuing.Maintenance.IsActive = true;
                                        foundActiveSection = true;
                                        break;
                                    }
                                }
                            }
                        }

                        // Si aucune section avec attribut type n'est trouvée, utiliser l'ancienne logique comme fallback
                        if (!foundActiveSection)
                        {
                            XElement delayInSecondsKey = sectionsFileAttente.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("DELAYINSECONDS", StringComparison.OrdinalIgnoreCase));
                            XElement maxActivesUsersKey = sectionsFileAttente.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("MAXACTIVESUSERS", StringComparison.OrdinalIgnoreCase));

                            if (delayInSecondsKey != null && maxActivesUsersKey != null)
                            {
                                int delayInSeconds = int.Parse(delayInSecondsKey.Value);
                                int maxActiveUsers = int.Parse(maxActivesUsersKey.Value);

                                if (maxActiveUsers > 0 && delayInSeconds > 0)
                                {
                                    FillQueuing(sectionsFileAttente, false);
                                    Queuing.QueuingV1.IsActive = true;
                                }
                                else if (maxActiveUsers == 0 && delayInSeconds == 0)
                                {
                                    FillQueuing(sectionsFileAttente, true);
                                    Queuing.Maintenance.IsActive = true;
                                }
                            }
                            else
                            {
                                // Par défaut QueuingV2
                                Queuing.QueuingV2.IsActive = true;
                            }
                        }

                        // Si aucune section n'a été trouvée du tout, activer QueuingV2 par défaut
                        if (!Queuing.QueuingV1.IsActive && !Queuing.QueuingV2.IsActive && !Queuing.Maintenance.IsActive)
                        {
                            Queuing.QueuingV2.IsActive = true;
                        }

                    }
                    else
                    {

                        //aucune section FILEATTENTE ==> on prends les valeurs par défaut
                    }
                }

                QueuingJson = JsonConvert.SerializeObject(Queuing);
            }
            catch (Exception ex)
            {
                log.Debug($"ex : {ex.Message}");

                throw ex;
            }
        }

        #region Méthodes ajax

        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string SaveQueuing(int _structureId, string _langCode, Queuing _queuing)
        {

            log.Debug($"SaveQueuing - structure id : {_structureId} langCode : {_langCode} ");

            string queuingStructuresJson = MyConfigurationManager.AppSettings("QueuingStructuresJson");

            string jsonString = File.ReadAllText(queuingStructuresJson);
            JsonRoot myDeserializedClass = JsonConvert.DeserializeObject<JsonRoot>(jsonString);
            ListConf jsonConf = myDeserializedClass.ListConf.Where(s => s.StructureId == _structureId).FirstOrDefault();


            string physiqueXmlPath = MyConfigurationManager.AppSettings("ThemisIniPath");
            physiqueXmlPath = physiqueXmlPath.Replace("[idstructureSur4zeros]", _structureId.ToString("0000"));

            log.Debug($"physiqueXmlPath : {physiqueXmlPath}");

            if (File.Exists(physiqueXmlPath))
            {
                XDocument doc = new XDocument();
                doc = XDocument.Load(physiqueXmlPath);

                IEnumerable<XElement> sectionsList = from section in doc.Elements("configIni").Elements("Section")
                                                     select section;

                int nbSections = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FILEATTENTE", StringComparison.OrdinalIgnoreCase)).Count();
                log.Debug($"nbSections : {nbSections}");


                XElement fileAttenteToDelete = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FILEATTENTE", StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                XElement fileAttenteQueuingV1ToDelete = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FileAttente_QUEUINGV1", StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                XElement fileAttenteQueuingV2ToDelete = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FileAttente_QUEUINGV2", StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                XElement fileAttenteMaintenanceToDelete = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FileAttente_Maintenance", StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                if (fileAttenteToDelete != null)
                {
                    fileAttenteToDelete.Remove();

                }
                if (fileAttenteQueuingV1ToDelete != null)
                {
                    fileAttenteQueuingV1ToDelete.Remove();

                }
                if (fileAttenteQueuingV2ToDelete != null)
                {
                    fileAttenteQueuingV2ToDelete.Remove();

                }
                if (fileAttenteMaintenanceToDelete != null)
                {

                    fileAttenteMaintenanceToDelete.Remove();
                }
                doc.Save(physiqueXmlPath);

                doc = XDocument.Load(physiqueXmlPath);


                //au moins une section FILEATTENTE

                IEnumerable<XElement> sectionsFileAttente = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FILEATTENTE", StringComparison.OrdinalIgnoreCase));
                IEnumerable<XElement> sectionsFileAttenteQueuingV1 = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FileAttente_QUEUINGV1", StringComparison.OrdinalIgnoreCase));
                IEnumerable<XElement> sectionsFileAttenteQueuingV2 = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FileAttente_QUEUINGV2", StringComparison.OrdinalIgnoreCase));
                IEnumerable<XElement> sectionsFileAttenteMaintenance = sectionsList.Where(a => a.Attribute("Name").Value.Equals("FileAttente_Maintenance", StringComparison.OrdinalIgnoreCase));


                log.Debug($"sectionsFileAttente count : {sectionsFileAttente.Count()}");
                log.Debug($"sectionsFileAttenteQueuingV1 count : {sectionsFileAttenteQueuingV1.Count()}");
                log.Debug($"sectionsFileAttenteQueuingV2 count : {sectionsFileAttenteQueuingV2.Count()}");
                log.Debug($"sectionsFileAttenteMaintenance count : {sectionsFileAttenteMaintenance.Count()}");


                if (sectionsFileAttente.Count() > 0)
                {
                    log.Debug($"step 2 ");

                    XElement delayInSecondsKey = sectionsFileAttente.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("DELAYINSECONDS", StringComparison.OrdinalIgnoreCase));
                    XElement maxActivesUsersKey = sectionsFileAttente.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("MAXACTIVESUSERS", StringComparison.OrdinalIgnoreCase));

                    IEnumerable<XElement> delayInSecondsExist = IsElementExist(sectionsFileAttente, "DELAYINSECONDS");
                    IEnumerable<XElement> maxActivesUsersExist = IsElementExist(sectionsFileAttente, "MAXACTIVESUSERS");

                    if (delayInSecondsKey != null && maxActivesUsersKey != null)
                    {
                        int delayInSeconds = int.Parse(sectionsFileAttente.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("DELAYINSECONDS", StringComparison.OrdinalIgnoreCase)).Value);
                        int maxActiveUsers = int.Parse(sectionsFileAttente.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("MAXACTIVESUSERS", StringComparison.OrdinalIgnoreCase)).Value);

                        log.Debug($"delayInSeconds {delayInSeconds}  maxActiveUsers {maxActiveUsers}");
                        if (maxActiveUsers > 0 && delayInSeconds > 0)
                        {
                            //récupérer queuing V1
                            QueuingUpdateValue(sectionsFileAttente, _queuing, false, _structureId);
                            log.Debug($"queuing V1 {_structureId}");

                        }
                        else if (maxActiveUsers == 0 && delayInSeconds == 0)
                        {
                            //récupérer maintenance
                            QueuingUpdateValue(sectionsFileAttente, _queuing, true, _structureId);
                            log.Debug($"maintenance {_structureId} ");
                        }
                    }
                    else
                    {
                        log.Debug($"QueuingV2 active {_structureId}");
                        //update le fichier json
                        CreateOrUpdateJsonFileQueuingV2(_structureId, ref myDeserializedClass, jsonConf, _queuing);
                    }
                }

                if (sectionsFileAttenteQueuingV1.Count() > 0)
                {
                    // FillQueuing(sectionsFileAttenteQueuingV1, false);

                    QueuingUpdateValue(sectionsFileAttenteQueuingV1, _queuing, false, _structureId);
                    log.Debug($"Fille attente : Queuing V1 ");
                }
                else
                {
                    CreateSectionNode(ref doc, _structureId, "v1", _queuing);
                }

                if (sectionsFileAttenteQueuingV2.Count() > 0)
                {
                    CreateOrUpdateJsonFileQueuingV2(_structureId, ref myDeserializedClass, jsonConf, _queuing);
                    log.Debug($"Fille attente : Queuing V2 {_structureId}");
                }
                else
                {
                    //la section QueuingV2 n'existe pas
                    //ajouter element queuingV2
                    CreateSectionNode(ref doc, _structureId, "v2", _queuing);

                }

                if (sectionsFileAttenteMaintenance.Count() > 0)
                {

                    QueuingUpdateValue(sectionsFileAttenteMaintenance, _queuing, true, _structureId);

                    log.Debug($"Fille attente : Maintenance {_structureId}");
                }
                else
                {

                    //la section Maintenance n'existe pas
                    CreateSectionNode(ref doc, _structureId, "maintenance", _queuing);

                }

                //enregistre le fichier XML
                doc.Save(physiqueXmlPath);

                if (_queuing.QueuingV2.IsActive)
                {
                    //si la queuingV2 est active, on vérifie que les tables/ procédures stockées existent
                    WSAdminClient wsAdmin = new WSAdminClient();


                    try
                    {
                        bool isQueuingDatabaseCreated = wsAdmin.CreateQueuing(_structureId);
                    }
                    catch (Exception ex)
                    {
                        log.Error("catch - GetListeConnexionsBDD dans relier connexion : " + _structureId + " " + ex.Message);
                        throw ex;
                    }
                    finally
                    {
                        if (wsAdmin.State == System.ServiceModel.CommunicationState.Opened)
                        {
                            wsAdmin.Close();
                        }
                    }
                }



                CreateOrUpdateJsonFileQueuingV2(_structureId, ref myDeserializedClass, jsonConf, _queuing);

                string output = JsonConvert.SerializeObject(myDeserializedClass, Formatting.Indented);
                File.WriteAllText(queuingStructuresJson, output);
            }
            return "ok";
        }

        /// <summary>
        /// Crée seulement les tables de file d'attente (extrait exact de SaveQueuing lignes 396-418)
        /// </summary>
        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string CreateQueueTables(int _structureId, string _langCode, Queuing _queuing)
        {
            try
            {
                // Création des tables directement, sans condition
                WSAdminClient wsAdmin = new WSAdminClient();

                try
                {
                    bool isQueuingDatabaseCreated = wsAdmin.CreateQueuing(_structureId);

                    if (!isQueuingDatabaseCreated)
                    {
                        // Le service peut retourner false si les tables existent déjà ou en cas d'erreur
                        return "Les tables de file d'attente existent déjà ou ont été créées avec succès.";
                    }
                }
                catch (Exception ex)
                {
                    log.Error($"CreateQueueTables - Erreur création tables pour structure {_structureId}: {ex.Message}");
                    throw ex;
                }
                finally
                {
                    if (wsAdmin.State == System.ServiceModel.CommunicationState.Opened)
                    {
                        wsAdmin.Close();
                    }
                }

                return "Tables de file d'attente créées avec succès !";
            }
            catch (Exception ex)
            {
                return $"Erreur lors de la création des tables : {ex.Message}";
            }
        }
        #endregion

        /// <summary>
        /// Modifie le fichier Json pour la queuing V2
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="myDeserializedClass"></param>
        /// <param name="jsonConf"></param>
        /// <param name="queuing"></param>
        public static void CreateOrUpdateJsonFileQueuingV2(int structureId, ref JsonRoot myDeserializedClass, ListConf jsonConf, Queuing queuing)
        {

            if (jsonConf != null)
            {
                jsonConf.IsOpen = (queuing.QueuingV2.IsOpen) ? "y" : "n";
                jsonConf.Iscaptcha = queuing.QueuingV2.IsCaptcha;
                jsonConf.MaxWT = queuing.QueuingV2.MaxActiveUsers;

                jsonConf.Npartick = queuing.QueuingV2.UsersPerTick;
                jsonConf.UrlToGo = queuing.QueuingV2.UrlToGo;
                jsonConf.DbType = queuing.QueuingV2.DbType;
                jsonConf.IsActiveFileAttente = queuing.QueuingV2.IsActiveFileAttente;
            }
            else
            {
                ListConf listConf = new ListConf()
                {
                    StructureId = structureId,
                    IsOpen = (queuing.QueuingV2.IsOpen) ? "y" : "n",
                    Iscaptcha = queuing.QueuingV2.IsCaptcha,
                    MaxWT = queuing.QueuingV2.MaxActiveUsers,
                    Npartick = queuing.QueuingV2.UsersPerTick,
                    UrlToGo = queuing.QueuingV2.UrlToGo,
                    DbType = queuing.QueuingV2.DbType,
                    IsActiveFileAttente = queuing.QueuingV2.IsActiveFileAttente
                };

                myDeserializedClass.ListConf.Add(listConf);
            }


        }

        public static void CreateSectionNode(ref XDocument doc, int structureId, string version, Queuing _queuing)
        {
            switch (version)
            {
                case "v1":
                    //la section QueuingV1 n'existe pas
                    if (_queuing.QueuingV1.IsActive)
                    {
                        XElement sectionQueuingV1Element = new XElement("Section", new XAttribute("Name", "FILEATTENTE"), new XAttribute("type", "v1"));

                        sectionQueuingV1Element.Add(new XElement("VIAQUEUINGSITE", _queuing.QueuingV1.ViaQueuingSite));

                        string queuingV1Url = MyConfigurationManager.AppSettings("QueuingV1Url").Replace("[fileName]", _queuing.QueuingV1.Url) + _queuing.QueuingV1.FileNameIndiv + MyConfigurationManager.AppSettings("QueuingFileExtension").Replace("[idstructureSur4zeros]", structureId.ToString("0000"));
                        string queuingV1IndivUrl = MyConfigurationManager.AppSettings("QueuingV1Url").Replace("[fileName]", _queuing.QueuingV1.Url) + _queuing.QueuingV1.FileNameIndiv + MyConfigurationManager.AppSettings("QueuingFileExtension").Replace("[idstructureSur4zeros]", structureId.ToString("0000"));
                        string queuingV1AboUrl = MyConfigurationManager.AppSettings("QueuingV1Url").Replace("[fileName]", _queuing.QueuingV1.Url) + _queuing.QueuingV1.FileNameAbo + MyConfigurationManager.AppSettings("QueuingFileExtension").Replace("[idstructureSur4zeros]", structureId.ToString("0000"));


                        sectionQueuingV1Element.Add(new XElement("QUEUINGPAGE", queuingV1Url)); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface
                        sectionQueuingV1Element.Add(new XElement("QUEUINGPAGEINDIV", queuingV1IndivUrl)); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface
                        sectionQueuingV1Element.Add(new XElement("QUEUINGPAGEABOV2", queuingV1AboUrl)); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface
                        // sectionQueuingV1Element.Add(new XElement("QUEUINGPAGE", _queuing.QueuingV1.Url.Replace("[fileName]", _queuing.QueuingV1.FileNameIndiv).Replace("[idstructureSur4zeros]", structureId.ToString("0000")))); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface
                        // sectionQueuingV1Element.Add(new XElement("QUEUINGPAGEINDIV", _queuing.QueuingV1.Url.Replace("[fileName]", _queuing.QueuingV1.FileNameIndiv).Replace("[idstructureSur4zeros]", structureId.ToString("0000")))); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface
                        //sectionQueuingV1Element.Add(new XElement("QUEUINGPAGEABOV2", _queuing.QueuingV1.Url.Replace("[fileName]", _queuing.QueuingV1.FileNameAbo).Replace("[idstructureSur4zeros]", structureId.ToString("0000")))); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface
                        sectionQueuingV1Element.Add(new XElement("MaxActivesUsers", _queuing.QueuingV1.MaxActiveUsers));
                        sectionQueuingV1Element.Add(new XElement("DelayInSeconds", _queuing.QueuingV1.DelayInSeconds));
                        sectionQueuingV1Element.Add(new XElement("FILENAMEINDIV", _queuing.QueuingV1.FileNameIndiv));
                        sectionQueuingV1Element.Add(new XElement("FILENAMEABOV2", _queuing.QueuingV1.FileNameAbo));

                        doc.Root.Add(sectionQueuingV1Element);
                    }
                    else
                    {
                        XElement sectionQueuingV1Element = new XElement("Section", new XAttribute("Name", "FILEATTENTE_QUEUINGV1"), new XAttribute("type", "v1"));

                        sectionQueuingV1Element.Add(new XElement("VIAQUEUINGSITE", _queuing.QueuingV1.ViaQueuingSite));


                        string queuingV1Url = MyConfigurationManager.AppSettings("QueuingV1Url").Replace("[fileName]", _queuing.QueuingV1.Url) + _queuing.QueuingV1.FileNameIndiv + MyConfigurationManager.AppSettings("QueuingFileExtension").Replace("[idstructureSur4zeros]", structureId.ToString("0000"));

                        sectionQueuingV1Element.Add(new XElement("QUEUINGPAGE", queuingV1Url));
                        //sectionQueuingV1Element.Add(new XElement("QUEUINGPAGE", _queuing.QueuingV1.Url.Replace("[fileName]", _queuing.QueuingV1.FileNameIndiv).Replace("[idstructureSur4zeros]", structureId.ToString("0000"))));
                        //sectionQueuingV1Element.Add(new XElement("QUEUINGPAGEINDIV", _queuing.QueuingV1.Url.Replace("[fileName]", _queuing.QueuingV1.FileNameIndiv).Replace("[idstructureSur4zeros]", structureId.ToString("0000")))); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface
                        //sectionQueuingV1Element.Add(new XElement("QUEUINGPAGEABOV2", _queuing.QueuingV1.Url.Replace("[fileName]", _queuing.QueuingV1.FileNameAbo).Replace("[idstructureSur4zeros]", structureId.ToString("0000")))); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface
                        sectionQueuingV1Element.Add(new XElement("MaxActivesUsers", _queuing.QueuingV1.MaxActiveUsers));
                        sectionQueuingV1Element.Add(new XElement("DelayInSeconds", _queuing.QueuingV1.DelayInSeconds));
                        sectionQueuingV1Element.Add(new XElement("FILENAMEINDIV", _queuing.QueuingV1.FileNameIndiv));
                        sectionQueuingV1Element.Add(new XElement("FILENAMEABOV2", _queuing.QueuingV1.FileNameAbo));

                        doc.Root.Add(sectionQueuingV1Element);
                    }

                    break;

                case "v2":
                    if (_queuing.QueuingV2.IsActive)
                    {

                        XElement sectionQueuingV2Element = new XElement("Section", new XAttribute("Name", "FILEATTENTE"), new XAttribute("type", "v2"));

                        sectionQueuingV2Element.Add(new XElement("VIAQUEUINGSITE", _queuing.QueuingV2.ViaQueuingSite));
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGE", _queuing.QueuingV2.Url.Replace("[idstructureSur4zeros]", structureId.ToString("0000")).Replace("[idstructure]", structureId.ToString())));
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGEINDIV", _queuing.QueuingV2.UrlIndiv.Replace("[idstructureSur4zeros]", structureId.ToString("0000")).Replace("[idstructure]", structureId.ToString())));
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGEABOV2", _queuing.QueuingV2.UrlAbo.Replace("[idstructureSur4zeros]", structureId.ToString("0000")).Replace("[idstructure]", structureId.ToString())));


                        /*
                        string defaultUrlQueuingPage = _queuing.QueuingV2.Url.Replace("[idstructure]", structureId.ToString()).Replace("[idstructureSur4zeros]", structureId.ToString("0000"));
                         
                         ML 02/02/2023 ajout d'un input dans la page permettant de modifier l'adresse url pour la queueingV2 
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGE", _queuing.QueuingV2.Url.Replace("[idstructure]", structureId.ToString()).Replace("[idstructureSur4zeros]", structureId.ToString("0000"))));
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGEINDIV", _queuing.QueuingV2.Url.Replace("[idstructure]", structureId.ToString()).Replace("[idstructureSur4zeros]", structureId.ToString("0000"))));
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGEABOV2", _queuing.QueuingV2.Url.Replace("[idstructure]", structureId.ToString()).Replace("[idstructureSur4zeros]", structureId.ToString("0000"))));
                        */


                        doc.Root.Add(sectionQueuingV2Element);
                    }
                    else
                    {
                        XElement sectionQueuingV2Element = new XElement("Section", new XAttribute("Name", "FILEATTENTE_QUEUINGV2"), new XAttribute("type", "v2"));

                        sectionQueuingV2Element.Add(new XElement("VIAQUEUINGSITE", _queuing.QueuingV2.ViaQueuingSite));
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGE", _queuing.QueuingV2.Url));
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGEINDIV", _queuing.QueuingV2.UrlIndiv));
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGEABOV2", _queuing.QueuingV2.UrlAbo));

                        /*
                         * sectionQueuingV2Element.Add(new XElement("QUEUINGPAGE", _queuing.QueuingV2.Url.Replace("[idstructure]", structureId.ToString()).Replace("[idstructureSur4zeros]", structureId.ToString("0000"))));
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGEINDIV", _queuing.QueuingV2.Url.Replace("[idstructure]", structureId.ToString()).Replace("[idstructureSur4zeros]", structureId.ToString("0000"))));
                        sectionQueuingV2Element.Add(new XElement("QUEUINGPAGEABOV2", _queuing.QueuingV2.Url.Replace("[idstructure]", structureId.ToString()).Replace("[idstructureSur4zeros]", structureId.ToString("0000"))));
                        */

                        doc.Root.Add(sectionQueuingV2Element);
                    }

                    break;


                case "maintenance":
                    string queuingMaintenanceUrl = MyConfigurationManager.AppSettings("MaintenanceUrl").Replace("[fileName]", _queuing.Maintenance.Url) + _queuing.Maintenance.FileName + MyConfigurationManager.AppSettings("QueuingFileExtension").Replace("[idstructureSur4zeros]", structureId.ToString("0000"));
                    string queuingMaintenanceIndivUrl = MyConfigurationManager.AppSettings("MaintenanceUrl").Replace("[fileName]", _queuing.Maintenance.Url) + _queuing.Maintenance.FileName + MyConfigurationManager.AppSettings("QueuingFileExtension").Replace("[idstructureSur4zeros]", structureId.ToString("0000"));
                    string queuingMaintenanceAboUrl = MyConfigurationManager.AppSettings("MaintenanceUrl").Replace("[fileName]", _queuing.Maintenance.Url) + _queuing.Maintenance.FileName + MyConfigurationManager.AppSettings("QueuingFileExtension").Replace("[idstructureSur4zeros]", structureId.ToString("0000"));

                    if (_queuing.Maintenance.IsActive)
                    {
                        XElement sectionMaintenanceElement = new XElement("Section", new XAttribute("Name", "FILEATTENTE"), new XAttribute("type", "maintenance"));

                        sectionMaintenanceElement.Add(new XElement("VIAQUEUINGSITE", _queuing.Maintenance.ViaQueuingSite));
                        sectionMaintenanceElement.Add(new XElement("QUEUINGPAGE", queuingMaintenanceUrl));
                        sectionMaintenanceElement.Add(new XElement("QUEUINGPAGEINDIV", queuingMaintenanceIndivUrl)); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface
                        sectionMaintenanceElement.Add(new XElement("QUEUINGPAGEABOV2", queuingMaintenanceAboUrl)); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface

                        //sectionMaintenanceElement.Add(new XElement("QUEUINGPAGE", _queuing.Maintenance.Url.Replace("[fileName]", _queuing.Maintenance.FileName).Replace("[idstructureSur4zeros]", structureId.ToString("0000"))));
                        sectionMaintenanceElement.Add(new XElement("MaxActivesUsers", _queuing.Maintenance.MaxActiveUsers));
                        sectionMaintenanceElement.Add(new XElement("DelayInSeconds", _queuing.Maintenance.DelayInSeconds));
                        sectionMaintenanceElement.Add(new XElement("FILENAME", _queuing.Maintenance.FileName));

                        doc.Root.Add(sectionMaintenanceElement);
                    }
                    else
                    {

                        XElement sectionMaintenanceElement = new XElement("Section", new XAttribute("Name", "FILEATTENTE_MAINTENANCE"), new XAttribute("type", "maintenance"));

                        sectionMaintenanceElement.Add(new XElement("VIAQUEUINGSITE", _queuing.Maintenance.ViaQueuingSite));
                        //sectionMaintenanceElement.Add(new XElement("QUEUINGPAGE", _queuing.Maintenance.Url.Replace("[fileName]", _queuing.Maintenance.FileName).Replace("[idstructureSur4zeros]", structureId.ToString("0000"))));
                        sectionMaintenanceElement.Add(new XElement("QUEUINGPAGE", queuingMaintenanceUrl));
                        sectionMaintenanceElement.Add(new XElement("QUEUINGPAGEINDIV", queuingMaintenanceIndivUrl)); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface
                        sectionMaintenanceElement.Add(new XElement("QUEUINGPAGEABOV2", queuingMaintenanceAboUrl)); //fusionne l'url de la queuing avec le nom de la page entré dans l'interface


                        sectionMaintenanceElement.Add(new XElement("MaxActivesUsers", _queuing.Maintenance.MaxActiveUsers));
                        sectionMaintenanceElement.Add(new XElement("DelayInSeconds", _queuing.Maintenance.DelayInSeconds));
                        sectionMaintenanceElement.Add(new XElement("FILENAME", _queuing.Maintenance.FileName));

                        doc.Root.Add(sectionMaintenanceElement);
                    }

                    break;

                default:
                    break;
            }



        }

        /// <summary>
        /// Recherche si un terme existe dans la liste xElements
        /// </summary>
        /// <param name="xElements">Elements du chier XML</param>
        /// <param name="searchTerm">terme à rechercher</param>
        /// <returns></returns>
        public static IEnumerable<XElement> IsElementExist(IEnumerable<XElement> xElements, string searchTerm)
        {

            IEnumerable<XElement> elementExist = (from el in xElements.Elements()
                                                  where el.Name.LocalName.Equals(searchTerm, StringComparison.OrdinalIgnoreCase)
                                                  select el);

            return elementExist;
        }

        public void FillQueuing(IEnumerable<XElement> xElements, bool isMaintenance)
        {

            IEnumerable<XElement> delayInSecondsExist = IsElementExist(xElements, "DELAYINSECONDS");
            IEnumerable<XElement> maxActivesUsersExist = IsElementExist(xElements, "MAXACTIVESUSERS");
            IEnumerable<XElement> queuingPageExist = IsElementExist(xElements, "QUEUINGPAGE");
            IEnumerable<XElement> queuingPageIndivExist = IsElementExist(xElements, "QUEUINGPAGEINDIV");
            IEnumerable<XElement> queuingPageAboV2Exist = IsElementExist(xElements, "QUEUINGPAGEABOV2");
            IEnumerable<XElement> queuingViaQueuingSiteExist = IsElementExist(xElements, "VIAQUEUINGSITE");
            IEnumerable<XElement> fileNameExist = IsElementExist(xElements, "FILENAME");
            IEnumerable<XElement> fileNameIndivExist = IsElementExist(xElements, "FILENAMEINDIV");
            IEnumerable<XElement> fileNameAboV2Exist = IsElementExist(xElements, "FILENAMEABOV2");

            if (isMaintenance)
            {
                if (delayInSecondsExist.Count() > 0)
                {
                    Queuing.Maintenance.DelayInSeconds = int.Parse(delayInSecondsExist.FirstOrDefault().Value);
                }
                else
                {
                    Queuing.Maintenance.DelayInSeconds = 0;
                }
                if (maxActivesUsersExist.Count() > 0)
                {
                    Queuing.Maintenance.MaxActiveUsers = int.Parse(maxActivesUsersExist.FirstOrDefault().Value);
                }
                else
                {
                    Queuing.Maintenance.MaxActiveUsers = 0;
                }


                if (fileNameExist.Count() > 0)
                {
                    Queuing.Maintenance.FileName = fileNameExist.FirstOrDefault().Value;
                }
                else
                {
                    if (queuingPageExist.Count() > 0)
                    {
                        string queuingUrl = queuingPageExist.FirstOrDefault().Value;
                        Queuing.Maintenance.FileName = Path.GetFileNameWithoutExtension(Path.GetFileName(new Uri(queuingUrl).AbsolutePath)).Replace($"_{structureId.ToString("0000")}", "");
                    }
                }

            }
            else
            {

                if (delayInSecondsExist.Count() > 0)
                {
                    Queuing.QueuingV1.DelayInSeconds = int.Parse(delayInSecondsExist.FirstOrDefault().Value);
                }
                else
                {
                    Queuing.QueuingV1.DelayInSeconds = 0;
                }
                if (maxActivesUsersExist.Count() > 0)
                {
                    Queuing.QueuingV1.MaxActiveUsers = int.Parse(maxActivesUsersExist.FirstOrDefault().Value);
                }
                else
                {
                    Queuing.QueuingV1.MaxActiveUsers = 0;
                }


                if (fileNameIndivExist.Count() > 0)
                {
                    Queuing.QueuingV1.FileNameIndiv = fileNameIndivExist.FirstOrDefault().Value;
                }
                else
                {
                    if (queuingPageIndivExist.Count() > 0)
                    {
                        string queuingUrl = queuingPageIndivExist.FirstOrDefault().Value;
                        Queuing.QueuingV1.FileNameIndiv = Path.GetFileNameWithoutExtension(Path.GetFileName(new Uri(queuingUrl).AbsolutePath)).Replace($"_{structureId.ToString("0000")}", "");
                    }
                }


                if (fileNameAboV2Exist.Count() > 0)
                {
                    Queuing.QueuingV1.FileNameAbo = fileNameAboV2Exist.FirstOrDefault().Value;
                }
                else
                {
                    if (queuingPageAboV2Exist.Count() > 0)
                    {
                        string queuingUrl = queuingPageAboV2Exist.FirstOrDefault().Value;
                        Queuing.QueuingV1.FileNameAbo = Path.GetFileNameWithoutExtension(Path.GetFileName(new Uri(queuingUrl).AbsolutePath)).Replace($"_{structureId.ToString("0000")}", "");
                    }

                }

            }

        }

        /// <summary>
        /// Mets à jour les clés ainsi que les nom de section pour la file attente V1 et maintenance
        /// </summary>
        /// <param name=""></param>
        /// <param name=""></param>
        public static void QueuingUpdateValue(IEnumerable<XElement> xElements, Queuing queuing, bool isMaintenance, int structureId)
        {
            if (isMaintenance)
            {


                XElement maintenanceQueuingUrl = xElements.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("QUEUINGPAGE", StringComparison.OrdinalIgnoreCase));
                if (maintenanceQueuingUrl != null)
                {
                    maintenanceQueuingUrl.Value = MyConfigurationManager.AppSettings("MaintenanceUrl").Replace("[fileName]", queuing.Maintenance.FileName) + MyConfigurationManager.AppSettings("QueuingFileExtension").Replace("[idstructureSur4zeros]", structureId.ToString("0000"));
                }

                XElement maintenanceQueuingFileName = xElements.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("FILENAME", StringComparison.OrdinalIgnoreCase));
                if (maintenanceQueuingFileName != null)
                {
                    maintenanceQueuingFileName.Value = queuing.Maintenance.FileName;
                }
                else
                {
                    //créer la clé


                }

                if (!queuing.Maintenance.IsActive)
                {
                    XElement sectionMaintenance = xElements.Where(at => at.Attribute("Name").Value.Equals("FILEATTENTE", StringComparison.OrdinalIgnoreCase)).SingleOrDefault();
                    if (sectionMaintenance != null)
                    {
                        sectionMaintenance.Attribute("Name").SetValue("FILEATTENTE_MAINTENANCE");
                    }
                }
                else
                {
                    XElement sectionMaintenance = xElements.Where(at => at.Attribute("Name").Value.Equals("FILEATTENTE_MAINTENANCE", StringComparison.OrdinalIgnoreCase)).SingleOrDefault();
                    if (sectionMaintenance != null)
                    {
                        sectionMaintenance.Attribute("Name").SetValue("FILEATTENTE");
                    }

                }
            }
            else
            {

                XElement fileAttenteMaxActiveUsers = xElements.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("MAXACTIVESUSERS", StringComparison.OrdinalIgnoreCase));
                if (fileAttenteMaxActiveUsers != null)
                {
                    fileAttenteMaxActiveUsers.Value = queuing.QueuingV1.MaxActiveUsers.ToString();
                }

                XElement fileAttenteDelayInSeconds = xElements.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("DELAYINSECONDS", StringComparison.OrdinalIgnoreCase));
                if (fileAttenteDelayInSeconds != null)
                {
                    fileAttenteDelayInSeconds.Value = queuing.QueuingV1.DelayInSeconds.ToString();
                }

                //INDIV
                XElement fileAttentePageIndiv = xElements.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("QUEUINGPAGEINDIV", StringComparison.OrdinalIgnoreCase));
                if (fileAttentePageIndiv != null)
                {
                    fileAttentePageIndiv.Value = MyConfigurationManager.AppSettings("QueuingV1Url") + queuing.QueuingV1.FileNameIndiv + MyConfigurationManager.AppSettings("QueuingFileExtension")
                        .Replace("[idstructure]", structureId.ToString("0000")).Replace("[idstructureSur4zeros]", structureId.ToString("0000"));
                }

                XElement fileAttenteFileNameIndiv = xElements.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("FILENAMEINDIV", StringComparison.OrdinalIgnoreCase));
                if (fileAttenteFileNameIndiv != null)
                {
                    fileAttenteFileNameIndiv.Value = queuing.QueuingV1.FileNameIndiv;
                }
                else
                {

                    //créer la clé
                }

                //ABOV2
                XElement fileAttenteQueuingPageAboV2 = xElements.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("QUEUINGPAGEABOV2", StringComparison.OrdinalIgnoreCase));
                if (fileAttenteQueuingPageAboV2 != null)
                {
                    fileAttenteQueuingPageAboV2.Value = MyConfigurationManager.AppSettings("QueuingV1Url") + queuing.QueuingV1.FileNameAbo + MyConfigurationManager.AppSettings("QueuingFileExtension").Replace("[idstructureSur4zeros]", structureId.ToString("0000"));
                }

                XElement fileAttenteFileNameAbov2 = xElements.Elements().FirstOrDefault(x => x.Name.LocalName.Equals("FILENAMEABOV2", StringComparison.OrdinalIgnoreCase));
                if (fileAttenteFileNameAbov2 != null)
                {
                    fileAttenteFileNameAbov2.Value = queuing.QueuingV1.FileNameAbo;
                }
                else
                {
                    //créer la clé

                }


                if (!queuing.QueuingV1.IsActive)
                {
                    XElement sectionQueuingV1 = xElements.Where(a => a.Attribute("Name").Value.Equals("FileAttente", StringComparison.OrdinalIgnoreCase)).SingleOrDefault();
                    if (sectionQueuingV1 != null)
                    {
                        sectionQueuingV1.Attribute("Name").SetValue("FILEATTENTE_QUEUINGV1");
                    }

                }
                else
                {
                    XElement sectionQueuingV1 = xElements.Where(a => a.Attribute("Name").Value.Equals("FILEATTENTE_QUEUINGV1", StringComparison.OrdinalIgnoreCase)).SingleOrDefault();
                    if (sectionQueuingV1 != null)
                    {
                        sectionQueuingV1.Attribute("Name").SetValue("FILEATTENTE");
                    }
                }
            }

        }
    }
}