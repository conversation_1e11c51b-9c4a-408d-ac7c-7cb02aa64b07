﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Principale.Master" AutoEventWireup="true" CodeBehind="QueuingSettings.aspx.cs" Inherits="login.pages.QueuingSettings" %>
<asp:Content ID="Content1" ContentPlaceHolderID="HeadContent" runat="server">
    <link href="../Styles/site_new.css" rel="stylesheet" type="text/css">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
	<%
		string QueuingFileExtension = ConfigurationSettings.AppSettings["QueuingFileExtension"].ToString().Replace("[idstructureSur4zeros]",  StructureId );
        string MaxNumberOfPeople = ConfigurationSettings.AppSettings["MaxNumberOfPeople"];
        string DelaiBeforeInactivity = ConfigurationSettings.AppSettings["DelaiBeforeInactivity"];
        string NumberOfPeoplePerPassage = ConfigurationSettings.AppSettings["NumberOfPeoplePerPassage"];
	%>
    <!--
	<%
        string maintenanceFileName = "";
        string maintenanceUrl = "";
        int maintenanceMaxActiveUsers = 0;
        int maintenanceDelayInSeconds = 0;
        bool maintenanceIsActive = false;

        string queuingV1FileNameIndiv = "";
        string queuingV1FileNameAbo = "";
        string queuingV1Url = "";
        int queuingV1MaxActiveUsers = 0;
        int queuingV1DelayInSeconds = 0;
        bool queuingV1IsActive = false;

        string queuingV2Url = "";
        string queuingV2UrlIndiv = "";
        string queuingV2UrlAbo = "";
        string queuingV2UrlToGo = "";
        int queuingV2MaxActiveUsers = 0;
        int queuingV2UsersPerTick = 0;
        bool queuingV2IsActive = false;
        bool queuingV2IsOpen = false;
        bool queuingV2IsCaptcha = false;
        string queuingV2DbType = "";
        bool queuingV2IsActiveFileAttente = false;

        if (Queuing != null)
        {
            maintenanceFileName = Queuing.Maintenance.FileName;
            maintenanceUrl = Queuing.Maintenance.Url;
            maintenanceMaxActiveUsers = Queuing.Maintenance.MaxActiveUsers;
            maintenanceDelayInSeconds = Queuing.Maintenance.DelayInSeconds;
            maintenanceIsActive = Queuing.Maintenance.IsActive;

            queuingV1FileNameIndiv = Queuing.QueuingV1.FileNameIndiv;
            queuingV1FileNameAbo = Queuing.QueuingV1.FileNameAbo;
            queuingV1Url = Queuing.QueuingV1.Url;
            queuingV1MaxActiveUsers = Queuing.QueuingV1.MaxActiveUsers;
            queuingV1DelayInSeconds = Queuing.QueuingV1.DelayInSeconds;
            queuingV1IsActive = Queuing.QueuingV1.IsActive;

            queuingV2Url =  Queuing.QueuingV2.Url;
            queuingV2UrlIndiv =  Queuing.QueuingV2.UrlIndiv;
            queuingV2UrlAbo =  Queuing.QueuingV2.UrlAbo;
            queuingV2IsActive = Queuing.QueuingV2.IsActive;
            queuingV2IsOpen = Queuing.QueuingV2.IsOpen;
            queuingV2IsCaptcha = Queuing.QueuingV2.IsCaptcha;

            queuingV2UrlToGo = Queuing.QueuingV2.UrlToGo;

            queuingV2MaxActiveUsers = Queuing.QueuingV2.MaxActiveUsers;
            queuingV2UsersPerTick = Queuing.QueuingV2.UsersPerTick;
            queuingV2DbType = Queuing.QueuingV2.DbType;
            queuingV2IsActiveFileAttente = Queuing.QueuingV2.IsActiveFileAttente;
        }
    %>
  
	StructureId : <%= StructureId %><br/>
    --------------- Maintenance ------------<br /> 
    maintenanceFileName :
    <%= maintenanceFileName %><br />
    maintenanceUrl :
    <%= maintenanceUrl %><br />
    maintenanceMaxActiveUsers :
    <%= maintenanceMaxActiveUsers %><br />
    maintenanceDelayInSeconds :
    <%= maintenanceDelayInSeconds %><br />
    maintenanceIsActive :
    <%= maintenanceIsActive %><br />
    --------------- queuingV1 ------------<br />
    queuingV1FileNameIndiv :
    <%= queuingV1FileNameIndiv %><br />
    queuingV1FileNameAbo :
    <%= queuingV1FileNameAbo %><br />
    queuingV1Url :
    <%= queuingV1Url %><br />
    queuingV1MaxActiveUsers :
    <%= queuingV1MaxActiveUsers %><br />
    queuingV1DelayInSeconds :
    <%= queuingV1DelayInSeconds %><br />
    queuingV1IsActive :
    <%= queuingV1IsActive %><br />
    --------------- queuingV2 ------------<br />
    queuingV2Url :
    <%= queuingV2Url %><br />
    queuingV2UrlIndiv :
    <%= queuingV2UrlIndiv %><br />
    queuingV2UrlAbo :
    <%= queuingV2UrlAbo %><br />
    queuingV2UrlToGo :
    <%= queuingV2UrlToGo %><br />
    queuingV2IsActive :
    <%= queuingV2IsActive %><br />
	 queuingV2IsCaptcha :
    <%= queuingV2IsCaptcha %><br />
	  queuingV2IsOpen :
    <%= queuingV2IsOpen %><br />
      queuingV2IsActiveFileAttente :
    <%= queuingV2IsActiveFileAttente %><br />
       queuingV2MaxActiveUsers :
    <%= queuingV2MaxActiveUsers %><br />
	  queuingV2UsersPerTick :
    <%= queuingV2UsersPerTick %><br />	
    <%= IsSuperAdmin %><br />	
	-->
    
    <h1><small data-trad="menu_queuing">File d'attente</small><br /><span data-trad="menu_queuing_settings">Paramétrage</span></h1>
    <div class="section_wrapper col-xs-12" id="queuingForm">
       
      
        <!-- maintenance start-->
        <div class="one_item_wrapper">
            <div class="row one_item_whiteblock is-table-row">
                <div class="col-xs-12 col-md-4" role="button" data-toggle="collapse" href="#collapseMaintenance" aria-expanded="false" aria-controls="collapseMaintenance">
                    <i class="icon-pause"></i> <strong data-trad="title_queuing_maintenance">Maintenance</strong>
                </div>
                <div class="col-xs-12 col-md-offset-5 col-md-3 text-right">
                    <input type="radio" name="radio-queuing_active" value="Maintenance" class="lcs_radio" autocomplete="off" <% if(maintenanceIsActive) { %> checked
                    <% } %> /> 

                    <% if (IsSuperAdmin)
                    { %>
                    <span class="action-buttons">
                        <span class="btn btn-default" role="button" data-toggle="collapse" href="#collapseMaintenance" aria-expanded="false" aria-controls="collapseMaintenance">
                            <i class="icon-chevron-down"></i>
                        </span>
                    </span>
                    <% } %> 
                </div>
            </div>

            <% if (IsSuperAdmin)
            { %>
            <div class="collapse row" id="collapseMaintenance">
                <div class="well">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label for="Maintenance_FileName" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_maintenance_name">Nom de la maintenance</label>
                            <div class="col-sm-6 col-md-3">
                                <div class="input-group">
                                	<input type="text" class="form-control" name="Maintenance_FileName" id="Maintenance_FileName" value="<%= maintenanceFileName %>" data-baseurl="<%= maintenanceUrl %>" >
                                	<div class="input-group-addon"><%= QueuingFileExtension %></div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-5">
                                <a href="<%= maintenanceUrl %>" target="_blank" id="maintenanceFileNameCompleteUrlLink"><i class="icon-external-link-sign" style="font-size: 1.5em;"></i></a>
                                <em><span data-trad="msg_queuing_final_url">l'adresse finale sera :</span> <span id="maintenanceFileNameCompleteUrlText"><%= maintenanceUrl %></span></em>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
             <% } %> 
        </div>
        <!-- maintenance end-->


        <!-- Queuing V1 start-->
        <div class="one_item_wrapper">
            <div class="row one_item_whiteblock is-table-row">
                <div class="col-xs-12 col-md-4" role="button" data-toggle="collapse" href="#collapseQueuingV1" aria-expanded="false" aria-controls="collapseQueuingV1">
                    <i class="icon-male"></i> <strong data-trad="title_queuing_v1">File d'attente V1</strong>
                </div>
                <div class="col-xs-12 col-md-offset-5 col-md-3 text-right">
                    <input type="radio" name="radio-queuing_active" value="QueuingV1" class="lcs_radio" autocomplete="off" <% if(queuingV1IsActive) { %> checked <% } %> /> 
                    <% if (IsSuperAdmin)
                    { %>
                    <span class="action-buttons">
                        <span class="btn btn-default" role="button" data-toggle="collapse" href="#collapseQueuingV1" aria-expanded="false" aria-controls="collapseQueuingV1">
                            <i class="icon-chevron-down"></i>
                        </span>
                    </span>
                    <% } %>
                </div>
            </div>
            <% if (IsSuperAdmin)
            { %>
            <div class="collapse row" id="collapseQueuingV1">
                <div class="well">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label for="QueuingV1_MaxActiveUsers" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v1_maxactiveusers">Nb. de personnes maximum</label>
                            <div class="col-sm-3 col-md-2">
                                <input type="number" class="form-control" name="QueuingV1_MaxActiveUsers" id="QueuingV1_MaxActiveUsers" value="<%= queuingV1MaxActiveUsers %>" max="<%= MaxNumberOfPeople %>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="QueuingV1_DelayInSeconds" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v1_delayinseconds">Délai avant l'inactivité</label>
                            <div class="col-sm-3 col-md-2">
                                <div class="input-group">
                                	<input type="number" class="form-control" name="QueuingV1_DelayInSeconds" id="QueuingV1_DelayInSeconds" value="<%= queuingV1DelayInSeconds %>" max="<%= DelaiBeforeInactivity %>"> 
	                                <div class="input-group-addon" data-trad="lbl_queuing_v1_seconds">sec.</div>
	                            </div>
                            </div>
                        </div>
                        <hr />
                        <div class="form-group">
                            <label for="QueuingV1_FileNameIndiv" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v1_filenameindiv">Nom de la file d’attente INDIV</label>
                            <div class="col-sm-6 col-md-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="QueuingV1_FileNameIndiv" id="QueuingV1_FileNameIndiv" value="<%= queuingV1FileNameIndiv %>" data-baseurl="<%= queuingV1Url %>">
                                    <div class="input-group-addon"><%= QueuingFileExtension %></div>
                                </div>
                                
                            </div>
                            <div class="col-sm-6 col-md-5">
                                <a href="<%= queuingV1Url %>" target="_blank" id="queuingV1FileNameIndivCompleteUrlLink"><i class="icon-external-link-sign" style="font-size: 1.5em;"></i></a>
                                <em><span data-trad="msg_queuing_final_url">l'adresse finale sera :</span> <span id="queuingV1FileNameIndivCompleteUrlText"><%= queuingV1FileNameIndiv %></span></em>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="QueuingV1_FileNameAbo" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v1_filenameabo">Nom de la file d’attente ABO</label>
                            <div class="col-sm-6 col-md-3">
                            	<div class="input-group">
	                                <input type="text" class="form-control" name="QueuingV1_FileNameAbo" id="QueuingV1_FileNameAbo" value="<%= queuingV1FileNameAbo %>" data-baseurl="<%= queuingV1Url %>" >
	                                <div class="input-group-addon"><%= QueuingFileExtension %></div>
	                            </div>
	                           
                            </div>
                            <div class="col-sm-6 col-md-5">
                                <a href="<%= queuingV1Url %>" target="_blank" id="queuingV1FileNameAboCompleteUrlLink"><i class="icon-external-link-sign" style="font-size: 1.5em;"></i></a>
                                <em><span data-trad="msg_queuing_final_url">l'adresse finale sera :</span> <span id="queuingV1FileNameAboCompleteUrlText"><%= queuingV1FileNameAbo %></span></em>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <% } %>
        </div>
        <!-- Queuing V1 end-->
        <!-- Queuing V2 start-->
        <div class="one_item_wrapper">
            <div class="row one_item_whiteblock is-table-row">
                <div class="col-xs-12 col-md-4" role="button" data-toggle="collapse" href="#collapseQueuingV2" aria-expanded="false" aria-controls="collapseQueuingV2">
                    <i class="icon-male"></i> <strong data-trad="title_queuing_v2">File d'attente V2</strong>
                </div>
                <div class="col-xs-12 col-md-offset-5 col-md-3 text-right">
                    <input type="radio" name="radio-queuing_active" value="QueuingV2" class="lcs_radio" autocomplete="off" <% if(queuingV2IsActive) { %> checked
                    <% } %> /> <span class="action-buttons"><span class="btn btn-default" role="button" data-toggle="collapse" href="#collapseQueuingV2" aria-expanded="false" aria-controls="collapseQueuingV2"><i class="icon-chevron-down"></i></span></span>
                </div>
            </div>
            <div class="collapse row" id="collapseQueuingV2">
                <div class="well">
                    <div class="form-horizontal">
                        <% if (IsSuperAdmin)
                        { %>
                        <div class="form-group">
                            <label for="QueuingV2_DbType" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v2_dbtype">Type de Base de données</label>
                            <div class="col-sm-3 col-md-2">
                                <select class="form-control" name="QueuingV2_DbType"  >
                                    <option value="sqlserver" <% if(queuingV2DbType=="sqlserver") { %> selected="selected" <% }  %> >Sql Server</option>
                                    <option value="mysql" <% if(queuingV2DbType=="mysql") { %> selected="selected" <% } %> >MySql</option>
                                </select>

                            </div>
                        </div>

                        <div class="form-group">
                            <label for="QueuingV2_MaxActiveUsers" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v2_maxactiveusers">Nb. de personnes maximum</label>
                            <div class="col-sm-3 col-md-2">
                                <input type="number" class="form-control" name="QueuingV2_MaxActiveUsers" id="QueuingV2_MaxActiveUsers" value="<%= queuingV2MaxActiveUsers %>" max="<%= MaxNumberOfPeople %>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="QueuingV2_UsersPerTick" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v2_userspertick">Nb. de personnes par passage</label>
                            <div class="col-sm-3 col-md-2">
                                <input type="number" class="form-control" name="QueuingV2_UsersPerTick" id="QueuingV2_UsersPerTick" value="<%= queuingV2UsersPerTick %>" max="<%= NumberOfPeoplePerPassage %>">
                            </div>
                        </div>

                        <hr />
                        <% } %>

                        <div class="form-group">
	                        <div class="checkbox">
							    <label class="col-sm-6 col-md-4 col-lg-2 control-label" for="QueuingV2_IsOpen" data-trad="lbl_queuing_v2_isopen">Ouverture de la file d'attente</label>
							    <div class="col-sm-6 col-md-3">
							    	<input type="checkbox" id="QueuingV2_IsOpen" name="QueuingV2_IsOpen" <% if(queuingV2IsOpen) { %> checked <% } %>/>
							    </div>
							</div>
						</div>

                        <div class="form-group" style="display: none;">
                            <div class="checkbox">
                                 <label class="col-sm-6 col-md-4 col-lg-2 control-label" for="QueuingV2_IsActiveFileAttente" data-trad="lbl_queuing_v2_IsActiveFileAttente">Activation de la file d'attente</label>
                                 <div class="col-sm-6 col-md-3">
                                      <input type="checkbox" id="QueuingV2_IsActiveFileAttente" name="QueuingV2_IsActiveFileAttente" <% if(queuingV2IsActiveFileAttente) { %> checked <% } %>/>
                                 </div>
                            </div>
                        </div>

                        <% if (IsSuperAdmin)
                        { %>
						<div class="form-group">
	                        <div class="checkbox">
							    <label class="col-sm-6 col-md-4 col-lg-2 control-label" for="QueuingV2_IsCaptcha" data-trad="lbl_queuing_v2_iscaptcha">Utilisation du google captcha V3</label>
							    <div class="col-sm-6 col-md-3">
							    	<input type="checkbox" id="QueuingV2_IsCaptcha" name="QueuingV2_IsCaptcha" <% if(queuingV2IsCaptcha) { %> checked <% } %>/>
							    </div>
							</div>
						</div>
                        <div class="form-group">
                            <label for="QueuingV2_UrlToGo" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v2_urltogo">Url de redirection vers la plateforme</label>
                            <div class="col-sm-6 col-md-3">
                                <input type="text" class="form-control" name="QueuingV2_UrlToGo" id="QueuingV2_UrlToGo" value="<%= queuingV2UrlToGo %>">
                            </div>
                            <div>
                                <a href="<%= queuingV2UrlToGo %>" target="_blank" id="queuingV2UrlToGoLink"><i class="icon-external-link-sign" style="font-size: 1.5em;"></i></a>
                            </div>
                        </div>

                        <hr />
                         
                        <div class="form-group">
                            <label for="queuingV2_Url" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v2_url">Url queuing générique</label>
                            <div class="col-sm-6 col-md-3">
                                <input type="text" class="form-control" name="QueuingV2_Url" id="queuingV2_Url" value="<%= queuingV2Url %>">
                            </div>
                            <div>
                                <a href="<%= queuingV2Url %>" target="_blank" id="queuingV2Url"><i class="icon-external-link-sign" style="font-size: 1.5em;"></i></a>
                                <a id="resetQueuingV2Url"><i class="icon-refresh" style="font-size: 1.5em;"></i></a>
                            </div>
                        </div>
                         <div class="form-group">
                            <label for="queuingV2_UrlIndiv" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v2_url_indiv">Url queuing pour l'indiv</label>
                            <div class="col-sm-6 col-md-3">
                                <input type="text" class="form-control" name="QueuingV2_UrlIndiv" id="queuingV2_UrlIndiv" value="<%= queuingV2UrlIndiv %>">
                            </div>
                            <div>
                                <a href="<%= queuingV2UrlIndiv %>" target="_blank" id="queuingV2UrlIndiv"><i class="icon-external-link-sign" style="font-size: 1.5em;"></i></a>
                                <a id="resetQueuingV2UrlIndiv"><i class="icon-refresh" style="font-size: 1.5em;"></i></a>
                            </div>
                        </div>
                         <div class="form-group">
                            <label for="queuingV2_UrlAbo" class="col-sm-6 col-md-4 col-lg-2 control-label" data-trad="lbl_queuing_v2_url_abo">Url queuing pour l'abo</label>
                            <div class="col-sm-6 col-md-3">
                                <input type="text" class="form-control" name="QueuingV2_UrlAbo" id="queuingV2_UrlAbo" value="<%= queuingV2UrlAbo %>">
                            </div>
                            <div>
                                <a href="<%= queuingV2UrlAbo %>" target="_blank" id="queuingV2UrlAbo"><i class="icon-external-link-sign" style="font-size: 1.5em;"></i></a>
                                <a id="resetQueuingV2UrlAbo"><i class="icon-refresh" style="font-size: 1.5em;"></i></a>
                            </div>
                        </div>

                        <% } %>


                    </div>
                </div>
            </div>
        </div>
        <!-- Queuing V2 end-->
    </div>

    <!-- Bouton pour créer une file d'attente -->
    <% if (IsSuperAdmin)
    { %>
    <div class="row">
        <div class="section_wrapper col-xs-12 text-left mt-2">
            <div class="btn btn-success" id="btnCreateQueue" data-trad="btn_create_queue">
                <i class="icon-plus"></i> Préparation d'une file d'attente
            </div>
        </div>
    </div>
    <% } %>

    <div class="row">
    	<div class="section_wrapper col-xs-12 text-right mt-2">
		     <div class="alert alert-warning text-center" role="alert" data-trad="msg_queuing_alert_warning_templates">
		     	Merci de vérifier l'existence de vos templates pour la Maintenance et la Queuing V1 avant de sauvegarder vos modifications.
		     </div>
    	</div>
        <div class="section_wrapper col-xs-12 text-right mt-2">
            <div class="btn btn-primary" id="btnSaveQueuing" data-trad="btn_queuing_save">Sauvegarder les modifications</div>
        </div>
    </div>

</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="FooterContent" runat="server">
    <script src="../assets/js/lc_switch/lc_switch.min.js" type="text/javascript"></script>
    <script type="text/javascript">
    $(document).ready(function(e) {
        lc_switch('.lcs_radio', {
            // (string) "checked" status label text
            on_txt: ReadXmlTranslate('lbl_queuing_switch_on'),
            // (string) "not checked" status label text
            off_txt: ReadXmlTranslate('lbl_queuing_switch_off'),
            // (string) custom "on" color. Supports also gradients
            on_color: false,
            // (bool) whether to enable compact mode
            compact_mode: false
        });
        lc_switch('#QueuingV2_IsOpen', {
            // (string) "checked" status label text
            on_txt: ReadXmlTranslate('lbl_queuing_switch_open'),
            // (string) "not checked" status label text
            off_txt: ReadXmlTranslate('lbl_queuing_switch_close'),
            // (string) custom "on" color. Supports also gradients
            on_color: "#1b6aaa",
            // (bool) whether to enable compact mode
            compact_mode: false
        });
        lc_switch('#QueuingV2_IsCaptcha', {
            // (string) "checked" status label text
            on_txt: ReadXmlTranslate('lbl_queuing_switch_on'),
            // (string) "not checked" status label text
            off_txt: ReadXmlTranslate('lbl_queuing_switch_off'),
            // (string) custom "on" color. Supports also gradients
            on_color: "#1b6aaa",
            // (bool) whether to enable compact mode
            compact_mode: false
        });
        lc_switch('#QueuingV2_IsActiveFileAttente', {
            // (string) "checked" status label text
            on_txt: ReadXmlTranslate('lbl_queuing_switch_open'),
            // (string) "not checked" status label text
            off_txt: ReadXmlTranslate('lbl_queuing_switch_close'),
            // (string) custom "on" color. Supports also gradients
            on_color: "#1b6aaa",
            // (bool) whether to enable compact mode
            compact_mode: false
        });
    })
    </script>
    <script>
        var DefaultQueuingV2GotoUrlIndiv = '<%= DefaultQueuingV2GotoUrlIndiv %>';
    	var QueuingFileExtension = '<%= ConfigurationSettings.AppSettings["QueuingFileExtension"].ToString().Replace("[idstructureSur4zeros]",  StructureId ) %>';
    	var QueuingJson = JSON.parse('<%= QueuingJson %>');
    	var LangCode = "<%= LangCode %>";
    	var StructureId = "<%= StructureId %>";
    </script>
    <script src="../assets/js/pages/queuing_settings.js" type="text/javascript"></script>

</asp:Content>