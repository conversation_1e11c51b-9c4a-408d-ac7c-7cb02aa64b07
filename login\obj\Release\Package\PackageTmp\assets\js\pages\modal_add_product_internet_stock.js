﻿var oTable;
var sAjaxSourceUrl = "parentPage/ProductsBasePage.aspx/";
var type;
var productId;

$(document).ready(function () {
    LoadPage();
});
function LoadPage() {

    LaunchTraduction();

    var productName = $('#productNameCurrent', $(parent.document)).text();
   // $('#lblTitleCreate').html($('#lblTitleCreate').html().replace("[productName]", productName));
    $('#lblTitleCreate').html(ReadXmlTranslate("add_new_product").replace("[productName]", productName));

    type = getUrlVars()['type'];
    productId = getUrlVars()['productId'];

    // Vérification et initialisation par défaut si les paramètres sont manquants
    if (type === undefined || type === null) {
        console.warn('Paramètre type manquant dans l\'URL');
        type = 0; // valeur par défaut
    }

    if (productId === undefined || productId === null) {
        console.warn('Paramètre productId manquant dans l\'URL');
        // Essayer de récupérer depuis le parent si possible
        var parentProductId = $('#productIdCurrent', $(parent.document)).text();
        if (parentProductId && parentProductId.trim() !== '') {
            productId = parentProductId.trim();
        } else {
            console.error('Impossible de récupérer productId');
            return; // Arrêter l'exécution si productId est indispensable
        }
    }

    console.log('Paramètres récupérés - type:', type, 'productId:', productId);

    $('#txtStartDate').datetimepicker({
        controlType: myControl
    });

    $('#txtEndDate').datetimepicker({
        controlType: myControl
    });

    $('#txtStartDate').css('position', 'relative').css('z-index', '100');
    $('#txtEndDate').css('position', 'relative').css('z-index', '100');



    $('input[name="rdStartDate"]').on('click', function () {
        // $('input[name="rdStartDate"]:checked').length > 0

        if ($('input[name="rdStartDate"]:checked').val() == "date") {
            $('#txtStartDate').parent().removeClass('hidden');
            $('#txtStartDateValidite').parent().addClass('hidden');
            $('#txtStartDateValidite').val('');
        }

        if ($('input[name="rdStartDate"]:checked').val() == "int") {
            $('#txtStartDateValidite').parent().removeClass('hidden');
            $('#txtStartDate').parent().addClass('hidden');
            $('#txtStartDate').val('');
        }
    });


    $('input[name="rdEndDate"]').on('click', function () {
        // $('input[name="rdStartDate"]:checked').length > 0

        if ($('input[name="rdEndDate"]:checked').val() == "date") {
            $('#txtEndDate').parent().removeClass('hidden');
            $('#txtEndDateValidite').parent().addClass('hidden');
            $('#txtEndDateValidite').val('');
        }

        if ($('input[name="rdEndDate"]:checked').val() == "int") {
            $('#txtEndDateValidite').parent().removeClass('hidden');
            $('#txtEndDate').parent().addClass('hidden');
            $('#txtEndDate').val('');
        }
    });

    //Affiche ou pas le résultat de toutes les sélections des étapes précédentes
    $("#id-check-horizontal").on('change', function () {
        if ($('#tablesResume').hasClass("desactive")) {
            $('#tablesResume').removeClass('desactive');
            $('#tablesResume').hide('slow');
        } else {
            $('#tablesResume').addClass('desactive');
            $('#tablesResume').html(''); ShowResumeTables();
            $('#tablesResume').show('slow');
        }
    }); // fin change id-check-horizontal

    GetListMaquettes();
}

//retour la liste des maquettes
function GetListMaquettes() {

    // Vérifier que productId est défini
    if (!productId || productId === undefined || productId === null) {
        console.error('GetListMaquettes: productId non défini');
        ShowError("error", "Erreur: ID du produit manquant", "alert alert-danger alert-dismissable", 5000);
        return;
    }

    var sData = JSON.stringify({ _produitInternet: productId, _isUpdate: false });


    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'GetListMaquettes',
        dataType: "json",
        data: sData,
        contentType: "application/json; charset=utf-8",     
        success: function (data) {

            //var parsed = $.parseJSON(data.d); 
            if ($('#indiv_model_choose').dataTable().fnGetData().length == 0) {
                ShowDataTablesMaquettes(oTable, data.d[0], data.d[1]);
            }


            var checkMaquette = false;
            $('.chkModelChooseTitle').on('click', function () {
                if ($(this).is(':checked')) {
                    checkMaquette = true;
                } else {
                    checkMaquette = false;
                }

                $.each($('input[name="chkTypeEnvoiId"]'), function (i, itemProduct) {
                    $(itemProduct).prop('checked', checkMaquette);
                });
            }); //fin du clic chkModelChooseTitle
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });
}


function ShowDataTablesMaquettes(oTable, dataProducts, dataMaquettes) {
    dataMaquettes.insert(0, { MaquetteId: 0, MaquetteNom: ReadXmlTranslate('aucune_maquette') });

    oTable = $('#indiv_model_choose').dataTable({
        "bDestroy": true,
        "bProcessing": true,
        "bSort": true,
        "bFilter": true,
        "bAutoWidth": false,
        "iDisplayLength": -1,
        "aaData": dataProducts,
        "bDeferRender": true,
        "aoColumnDefs": [
                { "bSortable": true,
                    "aTargets": [0],
                    "mRender": function (url, type, full) {
                        return '<input type="checkbox" class=" ace chkTypeEnvoiId" data-id="' + full.ProductId + '" name="chkTypeEnvoi" ><span class="lbl"></span></input>';
                    }
                }
           ],
        "aoColumns": [
                {
                    "mDataProp": "ProductId",
                    "sClass": "center",
                    "mRender": function (url, type, full) {
                        return '<input type="checkbox" class="ace chkTypeEnvoiId" data-id="' + full.ProductId + '" data-isobligatoire="' + full.NeedMaquette + '" name="chkTypeEnvoiId" ><span class="lbl"></span></input>';
                    },
                    //"sWidth": "5%",
                    "bSortable": false,
                    "bSearchable": false
                },
               {
                   "mDataProp": "ProductId",
                   //"sWidth": "25%",
                   "sClass": "center",
                   "mRender": function (url, type, full) {
                       var htmlselect = "<select id='selectMaquette_" + full.ProductId + "' data-productname='" + full.ProductNom + "' data-needmaquette='" + full.NeedMaquette + "'>";


                       //                       if (full.NeedMaquette == 0) {
                       //                           htmlselect += "<option value='" + dataMaquettes[0].MaquetteId + "'>" + dataMaquettes[0].MaquetteNom + "</option>";
                       //                       } else {
                       $.each(dataMaquettes, function (key, val) {
                           htmlselect += "<option value='" + val.MaquetteId + "'>" + val.MaquetteNom + "</option>";
                       });
                       // }

                       htmlselect += "</select>";

                       return htmlselect;
                   },
                   "bSortable": false,
                   "bSearchable": false
               },
              { "mDataProp": "ProductNom" }
        //{"mDataProp": "PlaceName" , "sWidth": "25%"}, 
         ]

           });

    //Change la lange du datatable
    SetLangeDataTable(oTable, 'indiv_model_choose');
}


//Récupère  un tableau d'objets les categories et les tarifs sélectionnés
function GetEventsIDSelected() {
    var arrEvent = new Array();
    $('input[name="chkEvent"]:checked').each(function () {
        arrEvent.push($(this).attr('idevent'));
    });
    return arrEvent;
}


function GetSessionsIDSelected() {
    var arrEvent = new Array();
    $('input[name="chkSession"]:checked').each(function () {
        arrEvent.push($(this).attr('idsessions'));
    });
    return arrEvent;
}



function ShowResumeTables() {
    var newTableEventsTree = $('#Eventstree').clone();
    //clone le tableau et desactive les checkbox 
    newTableEventsTree.find('input[type="checkbox"]').attr('disabled', 'disabled');


    //$('#resumeReserve').append(newTableReserve); 
    var newTableMaquette = $('#indiv_model_choose').clone();
    newTableMaquette.find('input[type="checkbox"]').attr('disabled', 'disabled');

    var $selects = $('#indiv_model_choose').find('select');

    newTableMaquette.find('select').val(function (index) {
        return $selects.eq(index).val();
    });


    newTableMaquette.find('select').attr('disabled', 'disabled');

    //$('#resumeMaquette').append(newTableMaquette); 
    $('#tablesResume').append(newTableEventsTree);
    $('#tablesResume').append(newTableMaquette);

}




function SaveProduitInternet(arrParamsGestionPlace) {

    //var events = GetEventsIDSelected();
    var sessions = GetSessionsIDSelected();
    var typeEnvoi = GetTypeEnvoiIdSelected();
    var typeProduit = getUrlParameter('type');
    
    var sData = JSON.stringify({ _productID: productId, _sessions: sessions, _typeEnvoi: typeEnvoi, _paramsGestionPlace: arrParamsGestionPlace, _typeProduit: typeProduit });

    try {
        $.ajax({
            type: "POST",
            url: sAjaxSourceUrl + 'SaveProduitInternet',
            data: sData,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            success: function (response) {

                ShowError("error", ReadXmlTranslate(response.d.split(':')[1]), "alert alert-" + response.d.split(':')[0] + " alert-dismissable", 5000);

                setTimeout(function () {
                    //$('#modalMessage').fadeIn(200);
                    parent.$.fancybox.close();
                }, 3000); // milliseconds
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
                console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            }
        });

    } catch (err) {

        //ShowModalError('modalMessage', 'erreur pendant l\'enregistrement', err, 'alert alert-danger alert-dismissable', 5000);
        ShowError("error", err, "alert alert-danger alert-dismissable", 5000);
    }
}


function getAmountProduitVariable() {

    var sData = JSON.stringify({ _produitInternet: productId });

    try {
        $.ajax({
            type: "POST",
            url: sAjaxSourceUrl + 'GetAmountProduitInternetByID',
            data: sData,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            success: function (response) {
                // Formatage du montant : conversion centimes vers euros
                $('#nbMin').val(response.d / 100);

            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                ShowError("error", XMLHttpRequest.statusText, "alert alert-danger alert-dismissable", 5000);
                //alert("Erreur pendant l'enregistrement de l'utilisateur :" + XMLHttpRequest.statusText);
            }
        });

    } catch (err) {
        ShowError("error", err, "alert alert-danger alert-dismissable", 5000);
    }
}


jQuery(function ($) {
    $('[data-rel=tooltip]').tooltip();
    // $('#fuelux-wizard').ace_wizard().on('change', function (e, info) {
    $('#fuelux-wizard').ace_wizard().on('change', function (e, info) {

        if (getUrlVars()['type'] == 0) {
            //si on est en type application ==> on cache les radio bouton "jusqu'à"
            $('input[name="rdStartDate"][value="int"]').closest('label').hide();
            $('input[name="rdEndDate"][value="int"]').closest('label').hide();

            //on click sur la radio button
            $('input[name="rdStartDate"][value="date"]').trigger('click');
            $('input[name="rdStartDate"][value="date"]').click();
            $('input[name="rdEndDate"][value="date"]').trigger('click');
            $('input[name="rdEndDate"][value="date"]').click();
        }

        if (info.step == 1 && info.direction == "next") {

            if (GetTypeEnvoiIdSelected().length > 0) {
                GetListMaquettes();
                getAmountProduitVariable();
            } else {
                // ShowError("error", "Sélectionnez au moins un type d'envoi", "alert alert-danger alert-dismissable", 5000);
                ShowError("error", ReadXmlTranslate('msg_error_select_type_envoi'), "alert alert-danger alert-dismissable", 3000);
                return false;
            }

        } else if (info.step == 2 && info.direction == "next") {
            var error = "<span id='errorNbMax' class='red'></span>";
            var arrParamsGestionPlace = [];
            obj = {};

            if ($('input[name="rdStartDate"]:checked').length == 0) {
                $('input[name="rdStartDate"]').parent().parent().addClass('has-error');

                //ShowModalError("modalMessage", "erreur", "La date de début doit être cochée", "alert alert-danger alert-dismissable", 5000);
                // ShowError("error", "La date de début doit être saisie", "alert alert-danger alert-dismissable", 5000);
                ShowError("error", ReadXmlTranslate('msg_error_date_debut_checked'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {
                if ($('#txtStartDate').val() != "" || $('#txtStartDateValidite').val() != "") {

                    if ($('#txtStartDateValidite').val() < 0) {
                        ShowError("error", ReadXmlTranslate('msg_error_start_date_negatif'), "alert alert-danger alert-dismissable", 3000);
                        return false;
                    } else {

                        $('input[name="rdStartDate"]').parent().parent().removeClass('has-error ');
                        $('input[name="rdStartDate"]').parent().parent().addClass('has-success ');


                        if ($('input[name="rdStartDate"]:checked').val() == "int") {
                            obj["StartDate"] = $('#txtStartDateValidite').val();
                            obj["rdStartDate"] = $('input[name="rdStartDate"]:checked').val();
                        } else {
                            obj["StartDate"] = $('#txtStartDate').val();
                            obj["rdStartDate"] = $('input[name="rdStartDate"]:checked').val();
                        }
                    }
                } else {
                    //ShowModalError("modalMessage", "erreur", "La date de début doit être saisie", "alert alert-danger alert-dismissable", 5000);
                    // ShowError("error", "La date de début doit être saisie", "alert alert-danger alert-dismissable", 5000);
                    ShowError("error", ReadXmlTranslate('msg_error_date_deb_empty'), "alert alert-danger alert-dismissable", 3000);
                }
            }

            if ($('input[name="rdEndDate"]:checked').length == 0) {
                $('input[name="rdEndDate"]').parent().parent().addClass('has-error');
                //ShowModalError("modalMessage", "erreur", "La date de fin doit être saisie", "alert alert-danger alert-dismissable", 5000);
                // ShowError("error", "La date de fin doit être saisie", "alert alert-danger alert-dismissable", 5000);
                ShowError("error", ReadXmlTranslate('msg_error_date_fin_checked'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {

                if ($('#txtEndDate').val() != "" || $('#txtEndDateValidite').val() != "") {

                    if ($('#txtEndDateValidite').val() < 0) {
                        ShowError("error", ReadXmlTranslate('msg_error_end_date_negatif'), "alert alert-danger alert-dismissable", 3000);
                        return false;
                    } else {
                        $('input[name="rdEndDate"]').parent().parent().removeClass('has-error ');
                        $('input[name="rdEndDate"]').parent().parent().addClass('has-success ');

                        if ($('input[name="rdEndDate"]:checked').val() == "int") {
                            obj["EndDate"] = $('#txtEndDateValidite').val();
                            obj["rdEndDate"] = $('input[name="rdEndDate"]:checked').val();
                        } else {
                            obj["EndDate"] = $('#txtEndDate').val();
                            obj["rdEndDate"] = $('input[name="rdEndDate"]:checked').val();
                        }
                        //obj["EndDate"] = ($('#txtEndDate').val() != "") ? $('#txtEndDate').val() : $('#txtEndDateValidite').val();

                    }
                } else {
                    //ShowModalError("modalMessage", "erreur", "La date de fin doit être saisie", "alert alert-danger alert-dismissable", 5000);
                    //ShowError("error", "La date de début doit être saisie", "alert alert-danger alert-dismissable", 5000);
                    ShowError("error", ReadXmlTranslate('msg_error_date_fin_empty'), "alert alert-danger alert-dismissable", 3000);
                }

            }


            if (!$.isNumeric($("#nbMin").val())) {
                $("#nbMin").parent().parent().addClass('has-error');
                //ShowModalError("modalMessage", "erreur", "Le nombre minimum de place n'est pas valide", "alert alert-danger alert-dismissable", 5000);
                //ShowError("error", "Le nombre minimum de place n'est pas valide", "alert alert-danger alert-dismissable", 5000);
                ShowError("error", ReadXmlTranslate('modal_select_min_max'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {

                $('#nbMin').parent().parent().removeClass('has-error ');
                $('#nbMin').parent().parent().addClass('has-success ');

            }


            if (!$.isNumeric($("#nbMax").val())) {
                // It isn't a number
                $("#nbMax").parent().parent().addClass('has-error');
                //ShowModalError("modalMessage", "erreur", "Le nombre maximum de place n'est pas valide", "alert alert-danger alert-dismissable", 5000);
                //ShowError("error", "Le nombre maximum de place n'est pas valide", "alert alert-danger alert-dismissable", 5000);
                ShowError("error", ReadXmlTranslate('msg_error_select_max'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {
                $('#nbMax').parent().parent().removeClass('has-error ');
                $('#nbMax').parent().parent().addClass('has-success ');
            }


            if (parseInt($("#nbMax").val()) < parseInt($("#nbMin").val())) {
                $("#nbMin").parent().parent().addClass('has-error');
                $("#nbMax").parent().parent().addClass('has-error');
                //ShowModalError("modalMessage", "erreur", "Le nombre maximum est inférieur au nombre minimum", "alert alert-danger alert-dismissable", 5000);
                // ShowError("error", "Le nombre maximum est inférieur au nombre minimum", "alert alert-danger alert-dismissable", 5000);
                ShowError("error", ReadXmlTranslate('modal_select_min_max'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {
                $("#nbMin").parent().parent().removeClass('has-error');
                $("#nbMax").parent().parent().removeClass('has-error');
                $("#nbMin").parent().parent().addClass('has-success');
                $("#nbMax").parent().parent().addClass('has-success');
            }

            if ($('#nbMin').val() < 0) {
                ShowError("error", ReadXmlTranslate('msg_error_min_negatif'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {
                // Reconversion euros vers centimes pour la sauvegarde
                obj["nbMin"] = Math.round($('#nbMin').val() * 100);
            }

            if ($('#nbMax').val() < 0) {
                ShowError("error", ReadXmlTranslate('msg_error_max_negatif'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {

                obj["nbMax"] = Math.round($('#nbMax').val() * 100);
            }


            if (!$.isNumeric($("#step").val())) {
                $("#step").parent().parent().addClass('has-error');
                ShowError("error", ReadXmlTranslate('msg_error_select_step'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {

                $('#step').parent().parent().removeClass('has-error ');
                $('#step').parent().parent().addClass('has-success ');

            }
               
            if ($('#step').val() < 0) {
                ShowError("error", ReadXmlTranslate('msg_error_step_negatif'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {
                obj["step"] = $('#step').val();
            }

            obj["DateDebValiditeType"] = $("select#startDateValiditeType option").filter(":selected").val();
            obj["DateFinValiditeType"] = $("select#endDateValiditeType option").filter(":selected").val();

            obj["TypeAcces"] = $('input[name="rdTypeAcces"]:checked').val();
            //ajoute au tableau l'objet 
            arrParamsGestionPlace.push(obj);


            SaveProduitInternet(arrParamsGestionPlace);

            return true;

        } else {

            if (info.direction == "next") {
                //ShowModalError("modalMessage", "erreur", "Selectionnez au moins un tarif pour une catégorie", "alert alert-danger alert-dismissable", 5000);
                // ShowError("error", "Selectionnez au moins un tarif pour une catégorie", "alert alert-danger alert-dismissable", 5000);
                ShowError("error", ReadXmlTranslate('msg_error_select_tarif'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {
                $("#resume").hide();
                return true;
            }
        }

    }).on('finished', function (e) {
        //(btnPdfExport 
        // ShowError("error", "Merci", "success"); 
        // ShowError("error", "L'ajout de la règle est terminée avec succès", "alert alert-success alert-dismissable", 5000);
        ShowError("error", ReadXmlTranslate('msg_success_insert_product'), "alert alert-danger alert-dismissable", 3000);

    }).on('stepclick', function (e) {
        return false;
        //prevent clicking on steps 
    });


}) 

